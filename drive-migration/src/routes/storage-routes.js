import express from "express";
import { storageScanner } from "../services/storage-scanner.js";
import { supabaseClient } from "../database/supabase.js";
import fs from "fs/promises";
import path from "path";

const router = express.Router();

/**
 * Start storage scan for users
 * POST /api/storage/scan
 */
router.post("/scan", async (req, res) => {
    try {
        const { userEmails, forceRefresh = false } = req.body;

        // Validate input
        if (!userEmails || !Array.isArray(userEmails) || userEmails.length === 0) {
            return res.status(400).json({
                error: "userEmails array is required"
            });
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const invalidEmails = userEmails.filter(email => !emailRegex.test(email));
        if (invalidEmails.length > 0) {
            return res.status(400).json({
                error: "Invalid email format(s) found",
                invalidEmails
            });
        }

        console.log(`📊 Starting storage scan for ${userEmails.length} users...`);

        // Start the storage scan
        const results = await storageScanner.scanAllUsersStorage(userEmails, forceRefresh);

        res.json({
            success: true,
            message: "Storage scan completed",
            results: {
                totalUsers: results.totalUsers,
                successful: results.success,
                failed: results.failed,
                errors: results.errors,
                duration: Date.now() - results.startTime
            }
        });

    } catch (error) {
        console.error("❌ Error in storage scan:", error.message);
        res.status(500).json({
            error: "Failed to scan storage",
            details: error.message
        });
    }
});

/**
 * Get storage statistics for all users
 * GET /api/storage/stats
 */
router.get("/stats", async (req, res) => {
    try {
        const {
            search = "",
            sortBy = "total_usage_bytes",
            sortOrder = "desc",
            page = 1,
            pageSize = 50
        } = req.query;

        // Build query
        let query = supabaseClient
            .getServiceClient()
            .from('user_storage_stats')
            .select('*', { count: 'exact' });

        // Apply search filter
        if (search) {
            query = query.ilike('user_email', `%${search}%`);
        }

        // Apply sorting (except for scanned_storage_bytes which will be sorted later)
        const ascending = sortOrder === 'asc';
        if (sortBy !== 'scanned_storage_bytes') {
            query = query.order(sortBy, { ascending });
        }

        // Apply pagination (except when sorting by scanned_storage_bytes)
        let offset, limit;
        if (sortBy !== 'scanned_storage_bytes') {
            offset = (parseInt(page) - 1) * parseInt(pageSize);
            query = query.range(offset, offset + parseInt(pageSize) - 1);
        }

        const { data: users, error, count } = await query;

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        // Add scanned storage size for each user using a single query
        if (users && users.length > 0) {
            const userEmails = users.map(user => user.user_email);

            // Get scanned storage for all users in one query
            const { data: scannedData, error: scannedError } = await supabaseClient
                .getServiceClient()
                .from('scanned_files')
                .select('user_email, size')
                .in('user_email', userEmails);

            // Group by user_email and sum sizes
            const scannedByUser = {};
            if (!scannedError && scannedData) {
                scannedData.forEach(file => {
                    if (!scannedByUser[file.user_email]) {
                        scannedByUser[file.user_email] = 0;
                    }
                    scannedByUser[file.user_email] += (file.size || 0);
                });
            }

            // Add scanned storage to each user
            users.forEach(user => {
                user.scanned_storage_bytes = scannedByUser[user.user_email] || 0;
            });

            // Handle sorting and pagination for scanned_storage_bytes
            if (sortBy === 'scanned_storage_bytes') {
                users.sort((a, b) => {
                    const aValue = a.scanned_storage_bytes || 0;
                    const bValue = b.scanned_storage_bytes || 0;
                    return ascending ? aValue - bValue : bValue - aValue;
                });

                // Apply pagination after sorting
                offset = (parseInt(page) - 1) * parseInt(pageSize);
                limit = parseInt(pageSize);
                const paginatedUsers = users.slice(offset, offset + limit);

                return res.json({
                    users: paginatedUsers,
                    pagination: {
                        totalCount: count || 0,
                        page: parseInt(page),
                        pageSize: parseInt(pageSize),
                        totalPages: Math.ceil((count || 0) / parseInt(pageSize))
                    },
                    overallStats: await storageScanner.getStorageStatistics(),
                    success: true
                });
            }
        }

        // Get overall statistics
        const overallStats = await storageScanner.getStorageStatistics();

        res.json({
            users: users || [],
            pagination: {
                totalCount: count || 0,
                page: parseInt(page),
                pageSize: parseInt(pageSize),
                totalPages: Math.ceil((count || 0) / parseInt(pageSize))
            },
            overallStats,
            success: true
        });

    } catch (error) {
        console.error("❌ Error getting storage stats:", error.message);
        res.status(500).json({
            error: "Failed to get storage statistics",
            details: error.message
        });
    }
});

/**
 * Get storage information for a specific user
 * GET /api/storage/user/:email
 */
router.get("/user/:email", async (req, res) => {
    try {
        const { email } = req.params;

        if (!email) {
            return res.status(400).json({
                error: "User email is required"
            });
        }

        const { data: user, error } = await supabaseClient
            .getServiceClient()
            .from('user_storage_stats')
            .select('*')
            .eq('user_email', email)
            .single();

        if (error && error.code !== 'PGRST116') {
            throw new Error(`Database error: ${error.message}`);
        }

        if (!user) {
            return res.status(404).json({
                error: "User storage data not found",
                message: "Please run storage scan first"
            });
        }

        res.json({
            user,
            success: true
        });

    } catch (error) {
        console.error("❌ Error getting user storage:", error.message);
        res.status(500).json({
            error: "Failed to get user storage information",
            details: error.message
        });
    }
});

/**
 * Update local downloaded bytes for a user
 * PUT /api/storage/user/:email/downloaded
 */
router.put("/user/:email/downloaded", async (req, res) => {
    try {
        const { email } = req.params;
        const { localDownloadedBytes, localFolderPath } = req.body;

        if (!email) {
            return res.status(400).json({
                error: "User email is required"
            });
        }

        if (typeof localDownloadedBytes !== 'number' || localDownloadedBytes < 0) {
            return res.status(400).json({
                error: "localDownloadedBytes must be a non-negative number"
            });
        }

        const updateData = {
            local_downloaded_bytes: localDownloadedBytes
        };

        if (localFolderPath) {
            updateData.local_folder_path = localFolderPath;
        }

        const { error } = await supabaseClient
            .getServiceClient()
            .from('user_storage_stats')
            .update(updateData)
            .eq('user_email', email);

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        res.json({
            success: true,
            message: "Local downloaded bytes updated successfully"
        });

    } catch (error) {
        console.error("❌ Error updating downloaded bytes:", error.message);
        res.status(500).json({
            error: "Failed to update downloaded bytes",
            details: error.message
        });
    }
});

/**
 * Calculate local folder sizes for all users
 * POST /api/storage/calculate-local-all
 */
router.post("/calculate-local-all", async (req, res) => {
    try {
        const { folderPath } = req.body;

        if (!folderPath) {
            return res.status(400).json({
                error: "folderPath is required"
            });
        }

        // Get all users from storage stats
        const { data: users, error: usersError } = await supabaseClient
            .getServiceClient()
            .from('user_storage_stats')
            .select('user_email');

        if (usersError) {
            throw new Error(`Database error: ${usersError.message}`);
        }

        if (!users || users.length === 0) {
            return res.status(404).json({
                error: "No users found in storage stats",
                message: "Please run storage scan first"
            });
        }

        const results = {
            successful: 0,
            failed: 0,
            errors: [],
            totalUsers: users.length,
            totalSize: 0
        };

        // Calculate folder size for each user
        for (const user of users) {
            try {
                const userFolderPath = path.join(folderPath, user.user_email);
                const folderSize = await calculateFolderSize(userFolderPath);

                // Update database
                const { error } = await supabaseClient
                    .getServiceClient()
                    .from('user_storage_stats')
                    .update({
                        local_downloaded_bytes: folderSize,
                        local_folder_path: folderPath
                    })
                    .eq('user_email', user.user_email);

                if (error) {
                    throw new Error(`Database error: ${error.message}`);
                }

                results.successful++;
                results.totalSize += folderSize;

                console.log(`✅ Calculated local size for ${user.user_email}: ${formatBytes(folderSize)}`);

            } catch (error) {
                console.error(`❌ Failed to calculate local size for ${user.user_email}:`, error.message);
                results.failed++;
                results.errors.push({
                    email: user.user_email,
                    error: error.message
                });
            }
        }

        res.json({
            success: true,
            message: "Batch local folder calculation completed",
            results: {
                ...results,
                totalSizeFormatted: formatBytes(results.totalSize)
            }
        });

    } catch (error) {
        console.error("❌ Error in batch local folder calculation:", error.message);
        res.status(500).json({
            error: "Failed to calculate local folder sizes",
            details: error.message
        });
    }
});

/**
 * Calculate local folder size for a user
 * POST /api/storage/calculate-local/:email
 */
router.post("/calculate-local/:email", async (req, res) => {
    try {
        const { email } = req.params;
        const { folderPath } = req.body;

        if (!email) {
            return res.status(400).json({
                error: "User email is required"
            });
        }

        if (!folderPath) {
            return res.status(400).json({
                error: "folderPath is required"
            });
        }

        // Calculate folder size
        const userFolderPath = path.join(folderPath, email);
        const folderSize = await calculateFolderSize(userFolderPath);

        // Update database
        const { error } = await supabaseClient
            .getServiceClient()
            .from('user_storage_stats')
            .update({
                local_downloaded_bytes: folderSize,
                local_folder_path: folderPath
            })
            .eq('user_email', email);

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        res.json({
            success: true,
            folderSize,
            folderSizeFormatted: formatBytes(folderSize),
            userFolderPath,
            message: "Local folder size calculated and updated"
        });

    } catch (error) {
        console.error("❌ Error calculating local folder size:", error.message);
        res.status(500).json({
            error: "Failed to calculate local folder size",
            details: error.message
        });
    }
});

/**
 * Calculate folder size recursively
 * @param {string} folderPath - Path to folder
 * @returns {Promise<number>} Total size in bytes
 */
async function calculateFolderSize(folderPath) {
    try {
        const stats = await fs.stat(folderPath);

        if (!stats.isDirectory()) {
            return stats.size;
        }

        const files = await fs.readdir(folderPath);
        let totalSize = 0;

        for (const file of files) {
            const filePath = path.join(folderPath, file);
            const fileStats = await fs.stat(filePath);

            if (fileStats.isDirectory()) {
                totalSize += await calculateFolderSize(filePath);
            } else {
                totalSize += fileStats.size;
            }
        }

        return totalSize;

    } catch (error) {
        if (error.code === 'ENOENT') {
            console.log(`Folder not found: ${folderPath}`);
            return 0;
        }
        throw error;
    }
}

/**
 * Format bytes to human readable format
 * @param {number} bytes - Bytes
 * @returns {string} Formatted string
 */
function formatBytes(bytes) {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export default router;
