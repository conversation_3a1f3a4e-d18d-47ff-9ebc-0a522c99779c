-- Optimized queries for common operations
-- Use these instead of the original slow queries

-- 1. Count files by mime type (OPTIMIZED)
-- Instead of: SELECT Count(*) FROM scanned_files WHERE mime_type NOT LIKE '%folder%'
-- Use this:
SELECT COUNT(*) FROM scanned_files 
WHERE mime_type NOT IN (
    'application/vnd.google-apps.folder',
    'folder'
);

-- 2. Count downloaded files (OPTIMIZED)
-- Instead of: SELECT Count(*) FROM scanned_files WHERE local_path IS NOT NULL
-- Use this with index:
SELECT COUNT(*) FROM scanned_files WHERE local_path IS NOT NULL;

-- 3. Get user file statistics (OPTIMIZED with materialized view)
-- Instead of complex aggregation on main table, use:
SELECT 
    user_email,
    SUM(file_count) as total_files,
    SUM(total_size) as total_size_bytes,
    pg_size_pretty(SUM(total_size)) as total_size_formatted
FROM mv_file_stats 
GROUP BY user_email 
ORDER BY SUM(total_size) DESC;

-- 4. Get files for specific user (OPTIMIZED)
SELECT * FROM scanned_files 
WHERE user_email = $1 
ORDER BY created_at DESC 
LIMIT 100;

-- 5. Get undownloaded files for user (OPTIMIZED with composite index)
SELECT file_id, name, size, mime_type 
FROM scanned_files 
WHERE user_email = $1 
  AND (download_status IS NULL OR download_status != 'downloaded')
ORDER BY size DESC;

-- 6. Get download statistics by mime type (OPTIMIZED)
SELECT 
    mime_type,
    SUM(file_count) as total_files,
    SUM(CASE WHEN download_status = 'downloaded' THEN file_count ELSE 0 END) as downloaded_files,
    SUM(total_size) as total_size
FROM mv_file_stats 
GROUP BY mime_type 
ORDER BY total_size DESC;

-- 7. Get recent activity (OPTIMIZED with index)
SELECT user_email, name, download_status, downloaded_at
FROM scanned_files 
WHERE downloaded_at IS NOT NULL
ORDER BY downloaded_at DESC 
LIMIT 50;

-- 8. Bulk update download status (OPTIMIZED)
-- Use batch updates instead of individual updates
UPDATE scanned_files 
SET download_status = 'downloaded',
    local_path = CASE 
        WHEN file_id = 'file1' THEN '/path/to/file1'
        WHEN file_id = 'file2' THEN '/path/to/file2'
        -- Add more cases as needed
    END,
    downloaded_at = NOW()
WHERE file_id IN ('file1', 'file2', 'file3', ...);

-- 9. Get storage usage by domain (OPTIMIZED)
SELECT 
    domain,
    total_files,
    pg_size_pretty(total_size) as total_size,
    downloaded_files,
    ROUND((downloaded_files::float / total_files * 100), 2) as download_percentage
FROM mv_user_summary
ORDER BY total_size DESC;

-- 10. Find duplicate files (OPTIMIZED)
SELECT name, mime_type, size, COUNT(*) as duplicate_count
FROM scanned_files 
WHERE size > 0
GROUP BY name, mime_type, size 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, size DESC;
