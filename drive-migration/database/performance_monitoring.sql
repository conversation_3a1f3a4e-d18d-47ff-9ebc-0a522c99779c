-- Performance monitoring queries
-- Use these to monitor database performance and identify issues

-- 1. Check slow queries (queries taking more than 1 second)
SELECT 
    query,
    calls,
    total_exec_time,
    mean_exec_time,
    max_exec_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_exec_time > 1000  -- More than 1 second
ORDER BY mean_exec_time DESC 
LIMIT 20;

-- 2. Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 10 THEN 'LOW_USAGE'
        ELSE 'ACTIVE'
    END as usage_status
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
  AND tablename = 'scanned_files'
ORDER BY idx_scan DESC;

-- 3. Check table and index sizes
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('scanned_files', 'mv_file_stats', 'mv_user_summary')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 4. Check materialized view freshness
SELECT 
    schemaname,
    matviewname,
    ispopulated,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||matviewname)) as size,
    (SELECT MAX(last_updated) FROM mv_file_stats) as last_refresh_file_stats,
    (SELECT MAX(last_scan) FROM mv_user_summary) as last_refresh_user_summary
FROM pg_matviews 
WHERE schemaname = 'public';

-- 5. Check buffer cache hit ratio (should be > 95%)
SELECT 
    'Buffer Cache Hit Ratio' as metric,
    ROUND(
        100.0 * sum(blks_hit) / (sum(blks_hit) + sum(blks_read)), 2
    ) as percentage
FROM pg_stat_database;

-- 6. Check connection and activity
SELECT 
    state,
    COUNT(*) as connection_count,
    AVG(EXTRACT(EPOCH FROM (now() - query_start))) as avg_duration_seconds
FROM pg_stat_activity 
WHERE state IS NOT NULL
GROUP BY state;

-- 7. Check lock waits
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS blocking_statement
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks 
    ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;

-- 8. Index advisor recommendations for current slow queries
SELECT 
    query,
    calls,
    mean_exec_time,
    (SELECT json_build_object(
        'recommended', array_length(index_statements, 1) > 0,
        'startup_cost_before', startup_cost_before,
        'startup_cost_after', startup_cost_after,
        'total_cost_before', total_cost_before,
        'total_cost_after', total_cost_after,
        'index_statements', index_statements
    ) FROM index_advisor(query)) as recommendations
FROM pg_stat_statements 
WHERE mean_exec_time > 500  -- More than 500ms
  AND (lower(query) LIKE '%select%' OR lower(query) LIKE '%update%')
ORDER BY mean_exec_time DESC 
LIMIT 10;
